import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { h } from 'vue';
import ModuleNameRenderer from './components/ModuleNameRenderer.vue';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '所属模块',
    align:"center",
    dataIndex: 'moduleId',
    width: 150,
    customRender: ({ text: moduleId }) => {
      if (!moduleId) return '无';
      return h(ModuleNameRenderer, { moduleId });
    },
   },
   {
    title: '分类名称',
    align:"center",
    dataIndex: 'categoryName'
   },
   {
    title: '分类编码',
    align:"center",
    dataIndex: 'categoryCode'
   },
   {
    title: '分类描述',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '封面图片URL',
    align:"center",
    dataIndex: 'coverImage'
   },
   {
    title: '父分类ID',
    align:"center",
    dataIndex: 'parentId'
   },
   {
    title: '层级 1-一级分类 2-二级分类',
    align:"center",
    dataIndex: 'level'
   },
   {
    title: '排序号',
    align:"center",
    dataIndex: 'sortOrder'
   },
   {
    title: '状态 0-禁用 1-启用',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '总视频数量',
    align:"center",
    dataIndex: 'totalVideos'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '所属模块ID',
    field: 'moduleId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入所属模块ID!'},
          ];
     },
  },
  {
    label: '分类名称',
    field: 'categoryName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入分类名称!'},
          ];
     },
  },
  {
    label: '分类编码',
    field: 'categoryCode',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入分类编码!'},
          ];
     },
  },
  {
    label: '分类描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '封面图片URL',
    field: 'coverImage',
    component: 'Input',
  },
  {
    label: '父分类ID',
    field: 'parentId',
    component: 'Input',
  },
  {
    label: '层级 1-一级分类 2-二级分类',
    field: 'level',
    component: 'InputNumber',
  },
  {
    label: '排序号',
    field: 'sortOrder',
    component: 'InputNumber',
  },
  {
    label: '状态 0-禁用 1-启用',
    field: 'status',
    component: 'InputNumber',
  },
  {
    label: '总视频数量',
    field: 'totalVideos',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  moduleId: {title: '所属模块ID',order: 0,view: 'text', type: 'string',},
  categoryName: {title: '分类名称',order: 1,view: 'text', type: 'string',},
  categoryCode: {title: '分类编码',order: 2,view: 'text', type: 'string',},
  description: {title: '分类描述',order: 3,view: 'textarea', type: 'string',},
  coverImage: {title: '封面图片URL',order: 4,view: 'text', type: 'string',},
  parentId: {title: '父分类ID',order: 5,view: 'text', type: 'string',},
  level: {title: '层级 1-一级分类 2-二级分类',order: 6,view: 'number', type: 'number',},
  sortOrder: {title: '排序号',order: 7,view: 'number', type: 'number',},
  status: {title: '状态 0-禁用 1-启用',order: 8,view: 'number', type: 'number',},
  totalVideos: {title: '总视频数量',order: 9,view: 'number', type: 'number',},
};

// 子分类表格列配置
export const subCategoriesColumns: BasicColumn[] = [
  {
    title: '分类名称',
    align: 'left',
    dataIndex: 'categoryName',
    width: 200,
  },
  {
    title: '分类编码',
    align: 'center',
    dataIndex: 'categoryCode',
    width: 120,
  },
  {
    title: '分类描述',
    align: 'left',
    dataIndex: 'description',
    ellipsis: true,
  },
  {
    title: '层级',
    align: 'center',
    dataIndex: 'level',
    width: 80,
  },
  {
    title: '排序号',
    align: 'center',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      return text === 1 ? '启用' : '禁用';
    },
  },
];

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
