<template>
  <div style="padding: 20px; border: 1px solid #ccc; margin: 10px;">
    <h3>模块API测试工具</h3>
    
    <div style="margin-bottom: 10px;">
      <label>模块ID: </label>
      <input v-model="testModuleId" placeholder="输入模块ID，如：1001" style="margin-right: 10px;" />
      <button @click="testApi" :disabled="loading">测试API</button>
    </div>
    
    <div v-if="loading" style="color: blue;">
      🔄 正在加载...
    </div>
    
    <div v-if="result" style="margin-top: 10px;">
      <h4>API调用结果:</h4>
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(result, null, 2) }}</pre>
    </div>
    
    <div v-if="error" style="margin-top: 10px; color: red;">
      <h4>错误信息:</h4>
      <pre style="background: #ffe6e6; padding: 10px; border-radius: 4px;">{{ JSON.stringify(error, null, 2) }}</pre>
    </div>
    
    <div style="margin-top: 20px;">
      <h4>使用说明:</h4>
      <p>1. 输入模块ID（如：1001）</p>
      <p>2. 点击"测试API"按钮</p>
      <p>3. 查看返回结果和控制台日志</p>
      <p>4. 检查网络面板中的请求详情</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { queryModuleById } from '../../inz_learning_modules/InzLearningModules.api';

  const testModuleId = ref('1001');
  const loading = ref(false);
  const result = ref(null);
  const error = ref(null);

  async function testApi() {
    if (!testModuleId.value) {
      alert('请输入模块ID');
      return;
    }

    loading.value = true;
    result.value = null;
    error.value = null;

    console.log('🧪 开始测试模块API');
    console.log('📋 测试参数:', { moduleId: testModuleId.value });

    try {
      const response = await queryModuleById(testModuleId.value);
      console.log('✅ API测试成功:', response);
      result.value = response;
    } catch (err) {
      console.error('❌ API测试失败:', err);
      error.value = {
        message: err.message,
        status: err.status,
        statusText: err.statusText,
        response: err.response?.data || err.response,
        config: {
          url: err.config?.url,
          method: err.config?.method,
          params: err.config?.params,
        }
      };
    } finally {
      loading.value = false;
    }
  }

  // 页面加载时自动测试
  testApi();
</script>

<style scoped>
  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
  }
  
  input {
    padding: 4px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  
  button {
    padding: 4px 12px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  
  button:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
</style>
