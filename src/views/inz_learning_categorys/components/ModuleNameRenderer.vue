<template>
  <span v-if="loading" class="module-loading">
    <Spin size="small" />
  </span>
  <span v-else-if="moduleName" class="module-name">
    {{ moduleName }}
  </span>
  <span v-else class="module-error">
    未知模块1
  </span>
</template>

<script setup lang="ts">
  import { ref, watchEffect } from 'vue';
  import { Spin } from 'ant-design-vue';
  import { queryModuleById } from '../../inz_learning_modules/InzLearningModules.api';

  // 组件属性定义
  interface Props {
    moduleId: string;
  }

  const props = defineProps<Props>();

  // 响应式数据
  const loading = ref(false);
  const moduleName = ref('');

  // 全局模块缓存 - 避免重复请求
  const moduleCache = new Map<string, string>();

  // 监听模块ID变化，自动加载模块信息
  watchEffect(async () => {
    if (!props.moduleId) {
      moduleName.value = '';
      return;
    }
    
    // 检查缓存中是否已有该模块信息
    if (moduleCache.has(props.moduleId)) {
      moduleName.value = moduleCache.get(props.moduleId)!;
      return;
    }
    
    // 开始加载模块信息
    loading.value = true;
    moduleName.value = '';
    
    try {
      console.log('开始加载模块信息，模块ID:', props.moduleId);
      
      const res = await queryModuleById(props.moduleId);
      console.log('模块API返回结果:', res);
      
      if (res.success && res.result) {
        const name = res.result.moduleName || '未知模块';
        moduleName.value = name;
        
        // 将结果缓存起来
        moduleCache.set(props.moduleId, name);
        console.log('模块信息加载成功:', name);
      } else {
        console.warn('模块API调用失败或数据为空:', res);
        moduleName.value = '';
        // 缓存空结果，避免重复请求
        moduleCache.set(props.moduleId, '');
      }
    } catch (error) {
      console.error('获取模块信息失败:', error);
      
      // 根据错误类型显示不同的提示
      if (error.message?.includes('Network')) {
        console.error('网络连接失败');
      } else if (error.status === 404) {
        console.warn('模块不存在:', props.moduleId);
      } else if (error.status === 403) {
        console.error('权限不足，无法查看模块信息');
      } else {
        console.error('未知错误:', error);
      }
      
      moduleName.value = '';
      // 缓存错误结果，避免重复请求
      moduleCache.set(props.moduleId, '');
    } finally {
      loading.value = false;
    }
  });

  // 清理缓存的方法（可选，用于内存管理）
  const clearCache = () => {
    moduleCache.clear();
  };

  // 获取缓存大小（用于调试）
  const getCacheSize = () => {
    return moduleCache.size;
  };

  // 导出方法供外部使用（可选）
  defineExpose({
    clearCache,
    getCacheSize,
  });
</script>

<style scoped>
  .module-loading {
    color: #999;
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }

  .module-name {
    color: #333;
    font-weight: normal;
  }

  .module-error {
    color: #999;
    font-style: italic;
    font-size: 12px;
  }

  /* 确保加载指示器与文本对齐 */
  .module-loading :deep(.ant-spin) {
    line-height: 1;
  }

  .module-loading :deep(.ant-spin-dot) {
    font-size: 12px;
  }
</style>
