<template>
  <div>
    <!-- 子分类表格 -->
    <BasicTable 
      bordered 
      size="small" 
      :loading="loading" 
      rowKey="id" 
      :canResize="false" 
      :columns="subCategoriesColumns" 
      :dataSource="dataSource" 
      :pagination="false"
      :scroll="{ x: 800 }"
      class="sub-categories-table"
    >
      <!-- 字段回显插槽 -->
      <template v-slot:bodyCell="{ column, record, index, text }">
        <!-- 可以在这里添加自定义渲染逻辑 -->
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watchEffect } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { subCategoriesColumns } from '../InzLearningCategorys.data';
  import { getSubCategories } from '../InzLearningCategorys.api';
  import { message } from 'ant-design-vue';

  // 组件属性定义
  interface Props {
    parentId: string;
  }

  const props = defineProps<Props>();

  // 响应式数据
  const loading = ref(false);
  const dataSource = ref([]);

  // 监听父ID变化，自动加载数据
  watchEffect(() => {
    if (props.parentId) {
      loadData(props.parentId);
    }
  });

  /**
   * 加载子分类数据
   * @param parentId 父分类ID
   */
  async function loadData(parentId: string) {
    if (!parentId) return;
    
    dataSource.value = [];
    loading.value = true;
    
    try {
      console.log('开始加载子分类数据，父分类ID:', parentId);
      
      const res = await getSubCategories(parentId);
      console.log('子分类API返回结果:', res);
      
      if (res.success) {
        // 处理返回的数据
        const result = res.result;
        if (result && result.records) {
          dataSource.value = result.records;
          console.log('子分类数据加载成功，共', result.records.length, '条记录');
        } else if (Array.isArray(result)) {
          // 如果直接返回数组
          dataSource.value = result;
          console.log('子分类数据加载成功，共', result.length, '条记录');
        } else {
          console.warn('子分类数据格式异常:', result);
          dataSource.value = [];
        }
      } else {
        console.warn('子分类API调用失败:', res);
        message.warning(res.message || '获取子分类数据失败');
        dataSource.value = [];
      }
    } catch (error) {
      console.error('获取子分类数据失败:', error);
      
      // 根据错误类型显示不同的提示
      if (error.message?.includes('Network')) {
        message.error('网络连接失败，请检查网络后重试');
      } else if (error.status === 403) {
        message.error('权限不足，无法查看子分类');
      } else if (error.status === 404) {
        message.warning('未找到相关子分类数据');
      } else {
        message.error('加载子分类失败，请稍后重试');
      }
      
      dataSource.value = [];
    } finally {
      loading.value = false;
    }
  }
</script>

<style lang="less" scoped>
  .sub-categories-table {
    margin-left: 20px; // 添加左侧缩进表示层级关系
    
    :deep(.ant-table) {
      background-color: #fafafa; // 浅灰色背景区分层级
    }
    
    :deep(.ant-table-thead > tr > th) {
      background-color: #f0f0f0; // 表头背景色
      font-size: 12px; // 稍小的字体
    }
    
    :deep(.ant-table-tbody > tr > td) {
      padding: 8px 12px; // 紧凑的内边距
      font-size: 12px; // 稍小的字体
    }
    
    :deep(.ant-table-tbody > tr:hover > td) {
      background-color: #e6f7ff; // 悬停效果
    }
  }
</style>
