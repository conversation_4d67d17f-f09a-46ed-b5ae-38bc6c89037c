import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/inz_learning_modules/inzLearningModules/list',
  save='/inz_learning_modules/inzLearningModules/add',
  edit='/inz_learning_modules/inzLearningModules/edit',
  deleteOne = '/inz_learning_modules/inzLearningModules/delete',
  deleteBatch = '/inz_learning_modules/inzLearningModules/deleteBatch',
  importExcel = '/inz_learning_modules/inzLearningModules/importExcel',
  exportXls = '/inz_learning_modules/inzLearningModules/exportXls',
  queryById = '/inz_learning_modules/inzLearningModules/queryById', // 新增根据ID查询模块接口
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 根据ID查询模块信息
 * @param id 模块ID
 */
export const queryModuleById = (id: string) => {
  return defHttp.get({
    url: Api.queryById,
    params: { id },
  });
};
